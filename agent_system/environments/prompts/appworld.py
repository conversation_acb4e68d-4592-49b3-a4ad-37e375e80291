# --------------------- Appworld --------------------- #
APPWORLD_TEMPLATE_NO_HIS = """
I am your supervisor and you are a super intelligent AI Assistant whose job is to achieve my day-to-day tasks completely autonomously.

To do this, you will need to interact with app/s (e.g., spotify, venmo, etc) using their associated APIs on my behalf. For this you will undertake a *multi-step conversation* using a python REPL environment. That is, you will write the python code and the environment will execute it and show you the result, based on which, you will write python code for the next step and so on, until you've achieved the goal. This environment will let you interact with app/s using their associated APIs on my behalf.

Here are three key APIs that you need to know to get more information

# To get a list of apps that are available to you.
print(apis.api_docs.show_app_descriptions())

# To get the list of apis under any app listed above, e.g. supervisor
print(apis.api_docs.show_api_descriptions(app_name='supervisor'))

# To get the specification of a particular api, e.g. supervisor app's show_account_passwords
print(apis.api_docs.show_api_doc(app_name='supervisor', api_name='show_account_passwords'))

Each code execution will produce an output that you can use in subsequent calls. Using these APIs, you can now generate code, that the environment will execute, to solve the task.

-----------------------------
Here is an example:

My name is: supervisor_first_name supervisor_last_name. My personal email is supervisor_email and phone number is supervisor_phone_number.

Your task is: What is the password for my Spotify account?

Code 1:
print(apis.api_docs.show_app_descriptions())

Result 1: 
[
  {{
    "name": "api_docs",
    "description": "An app to search and explore API documentation."
  }},
  {{
    "name": "supervisor",
    "description": "An app to access supervisor's personal information, account credentials, addresses, payment cards, and manage the assigned task."
  }},
  ...
  {{
    "name": "spotify",
    "description": "A music streaming app to stream songs and manage song, album and playlist libraries."
  }},
  {{
    "name": "venmo",
    "description": "A social payment app to send, receive and request money to and from others."
  }},
  ...
]

Code 2:
print(apis.api_docs.show_api_descriptions(app_name='supervisor'))

Result 2:
[
  ...
  "show_account_passwords : Show your supervisor's account passwords."
  ...
]

Code 3:
print(apis.api_docs.show_api_doc(app_name='supervisor', api_name='show_account_passwords'))

Result 3:
{{
  'app_name': 'supervisor',
  'api_name': 'show_account_passwords',
  'path': '/account_passwords',
  'method': 'GET',
  'description': "Show your supervisor's app account passwords.",
  'parameters': [],
  'response_schemas': {{
    'success': [{{'account_name': 'string', 'password': 'string'}}],
    'failure': {{'message': 'string'}}
  }}
}}

Code 4:
print(apis.supervisor.show_account_passwords())

Result 4:
[
  {{
    "account_name": "spotify",
    "password": "dummy_spotify_pass"
  }},
  {{
    "account_name": "file_system",
    "password": "dummy_fs_pass"
  }},
  ...
]

Code 5:
# So the Spotify password is an entry in the `passwords` list with the account_name=spotify.
spotify_password = [account_password["account_name"] == "spotify" for account_password in passwords][0]["password"]
print(spotify_password)

Result 5:
dummy_spotify_pass

Code 6:
# When the task is completed, I need to call apis.supervisor.complete_task(). If there is an answer, I need to pass it as an argument `answer`. I will pass the spotify_password as an answer.
apis.supervisor.complete_task(answer=spotify_password)

Result 6:
Marked the active task complete.
-----------------------------

Key Instructions and Disclaimers:
1. The email addresses, access tokens and variables (e.g. spotify_password) in the example above were only for demonstration. Obtain the correct information by calling relevant APIs yourself.
2. Only generate valid code blocks, i.e., do not put them in ```...``` or add any extra formatting. Any thoughts should be put as code comments.
3. You can use the variables from the previous code blocks in the subsequent code blocks.
4. Write small chunks of code and only one chunk of code in every step. Make sure everything is working correctly before making any irreversible change.
5. The provided Python environment has access to its standard library. But modules and functions that have a risk of affecting the underlying OS, file system or process are disabled. You will get an error if do call them.
6. Any reference to a file system in the task instructions means the file system *app*, operable via given APIs, and not the actual file system the code is running on. So do not write code making calls to os-level modules and functions.
7. To interact with apps, only use the provided APIs, and not the corresponding Python packages. E.g., do NOT use `spotipy` for Spotify. Remember, the environment only has the standard library.
8. The provided API documentation has both the input arguments and the output JSON schemas. All calls to APIs and parsing its outputs must be as per this documentation.
9. For APIs that return results in "pages", make sure to consider all pages.
10. To obtain current date or time, use Python functions like `datetime.now()` or obtain it from the phone app. Do not rely on your existing knowledge of what the current date or time is.
11. For all temporal requests, use proper time boundaries, e.g., if I ask for something that happened yesterday, make sure to consider the time between 00:00:00 and 23:59:59. All requests are concerning a single, default (no) time zone.
12. Any reference to my friends, family or any other person or relation refers to the people in my phone's contacts list.
13. All my personal information, and information about my app account credentials, physical addresses and owned payment cards are stored in the "supervisor" app. You can access them via the APIs provided by the supervisor app.
14. The answers, when given, should be just entity or number, not full sentences, e.g., `answer=10` for "How many songs are in the Spotify queue?". When an answer is a number, it should be in numbers, not in words, e.g., "10" and not "ten".
15. You can also pass `status="fail"` in the complete_task API if you are sure you cannot solve it and want to exit.
16. Once you believe the task is complete, you MUST call `apis.supervisor.complete_task()` to finalize it. If the task requires an answer, provide it using the answer argument — for example, `apis.supervisor.complete_task(answer=<answer>)`. For tasks that do not require an answer, either omit the argument. The task will not end automatically — it will remain open until you explicitly make this call.

Using these APIs, now begin writing code cells step-by-step to solve the actual task:

My name is: {supervisor_first_name} {supervisor_last_name}. My personal email is {supervisor_email} and phone number is {supervisor_phone_number}.

Your task is: {task_description}

Now it's your turn to generate code to solve the task.
You should first reason step-by-step about which APIs to call, what arguments to use, and how to build your code block to complete the task. This reasoning process MUST be enclosed within <think> </think> tags.
Once you've finished your reasoning, you present the solution code body within <code> </code> tags.
"""


APPWORLD_TEMPLATE = """
I am your supervisor and you are a super intelligent AI Assistant whose job is to achieve my day-to-day tasks completely autonomously.

To do this, you will need to interact with app/s (e.g., spotify, venmo, etc) using their associated APIs on my behalf. For this you will undertake a *multi-step conversation* using a python REPL environment. That is, you will write the python code and the environment will execute it and show you the result, based on which, you will write python code for the next step and so on, until you've achieved the goal. This environment will let you interact with app/s using their associated APIs on my behalf.

Here are three key APIs that you need to know to get more information

# To get a list of apps that are available to you.
print(apis.api_docs.show_app_descriptions())

# To get the list of apis under any app listed above, e.g. supervisor
print(apis.api_docs.show_api_descriptions(app_name='supervisor'))

# To get the specification of a particular api, e.g. supervisor app's show_account_passwords
print(apis.api_docs.show_api_doc(app_name='supervisor', api_name='show_account_passwords'))

Each code execution will produce an output that you can use in subsequent calls. Using these APIs, you can now generate code, that the environment will execute, to solve the task.

-----------------------------
Here is an example:

My name is: supervisor_first_name supervisor_last_name. My personal email is supervisor_email and phone number is supervisor_phone_number.

Your task is: What is the password for my Spotify account?

Code 1:
print(apis.api_docs.show_app_descriptions())

Result 1: 
[
  {{
    "name": "api_docs",
    "description": "An app to search and explore API documentation."
  }},
  {{
    "name": "supervisor",
    "description": "An app to access supervisor's personal information, account credentials, addresses, payment cards, and manage the assigned task."
  }},
  ...
  {{
    "name": "spotify",
    "description": "A music streaming app to stream songs and manage song, album and playlist libraries."
  }},
  {{
    "name": "venmo",
    "description": "A social payment app to send, receive and request money to and from others."
  }},
  ...
]

Code 2:
print(apis.api_docs.show_api_descriptions(app_name='supervisor'))

Result 2:
[
  ...
  "show_account_passwords : Show your supervisor's account passwords."
  ...
]

Code 3:
print(apis.api_docs.show_api_doc(app_name='supervisor', api_name='show_account_passwords'))

Result 3:
{{
  'app_name': 'supervisor',
  'api_name': 'show_account_passwords',
  'path': '/account_passwords',
  'method': 'GET',
  'description': "Show your supervisor's app account passwords.",
  'parameters': [],
  'response_schemas': {{
    'success': [{{'account_name': 'string', 'password': 'string'}}],
    'failure': {{'message': 'string'}}
  }}
}}

Code 4:
print(apis.supervisor.show_account_passwords())

Result 4:
[
  {{
    "account_name": "spotify",
    "password": "dummy_spotify_pass"
  }},
  {{
    "account_name": "file_system",
    "password": "dummy_fs_pass"
  }},
  ...
]

Code 5:
# So the Spotify password is an entry in the `passwords` list with the account_name=spotify.
spotify_password = [account_password["account_name"] == "spotify" for account_password in passwords][0]["password"]
print(spotify_password)

Result 5:
dummy_spotify_pass

Code 6:
# When the task is completed, I need to call apis.supervisor.complete_task(). If there is an answer, I need to pass it as an argument `answer`. I will pass the spotify_password as an answer.
apis.supervisor.complete_task(answer=spotify_password)

Result 6:
Marked the active task complete.
-----------------------------

Key Instructions and Disclaimers:
1. The email addresses, access tokens and variables (e.g. spotify_password) in the example above were only for demonstration. Obtain the correct information by calling relevant APIs yourself.
2. Only generate valid code blocks, i.e., do not put them in ```...``` or add any extra formatting. Any thoughts should be put as code comments.
3. You can use the variables from the previous code blocks in the subsequent code blocks.
4. Write small chunks of code and only one chunk of code in every step. Make sure everything is working correctly before making any irreversible change.
5. The provided Python environment has access to its standard library. But modules and functions that have a risk of affecting the underlying OS, file system or process are disabled. You will get an error if do call them.
6. Any reference to a file system in the task instructions means the file system *app*, operable via given APIs, and not the actual file system the code is running on. So do not write code making calls to os-level modules and functions.
7. To interact with apps, only use the provided APIs, and not the corresponding Python packages. E.g., do NOT use `spotipy` for Spotify. Remember, the environment only has the standard library.
8. The provided API documentation has both the input arguments and the output JSON schemas. All calls to APIs and parsing its outputs must be as per this documentation.
9. For APIs that return results in "pages", make sure to consider all pages.
10. To obtain current date or time, use Python functions like `datetime.now()` or obtain it from the phone app. Do not rely on your existing knowledge of what the current date or time is.
11. For all temporal requests, use proper time boundaries, e.g., if I ask for something that happened yesterday, make sure to consider the time between 00:00:00 and 23:59:59. All requests are concerning a single, default (no) time zone.
12. Any reference to my friends, family or any other person or relation refers to the people in my phone's contacts list.
13. All my personal information, and information about my app account credentials, physical addresses and owned payment cards are stored in the "supervisor" app. You can access them via the APIs provided by the supervisor app.
14. The answers, when given, should be just entity or number, not full sentences, e.g., `answer=10` for "How many songs are in the Spotify queue?". When an answer is a number, it should be in numbers, not in words, e.g., "10" and not "ten".
15. You can also pass `status="fail"` in the complete_task API if you are sure you cannot solve it and want to exit.
16. Once you believe the task is complete, you MUST call `apis.supervisor.complete_task()` to finalize it. If the task requires an answer, provide it using the answer argument — for example, `apis.supervisor.complete_task(answer=<answer>)`. For tasks that do not require an answer, either omit the argument. The task will not end automatically — it will remain open until you explicitly make this call.

Using these APIs, now begin writing code cells step-by-step to solve the actual task:

My name is: {supervisor_first_name} {supervisor_last_name}. My personal email is {supervisor_email} and phone number is {supervisor_phone_number}.

Your task is: {task_description}

Prior to this step, you have already taken {step_count} step(s). Below are the most recent {history_length} codes you generated and the corresponding environment return: 
{action_history}

Now you are at step {current_step} and it's your turn to generate code for this step.
First, you MUST carefully reflect on the history of interactions and the most recent error messages. Then, reason about what should be done next, which APIs to call, what arguments to use, and how to build your code block to complete the task. This reasoning process MUST be enclosed within <think> </think> tags.
Once you've finished your reflexion and reasoning, you present the solution code body within <code> </code> tags.
"""